"use client";

import { TRoleList } from "@/config/permissions/types";
import { getUserMainRole } from "@/core/auth/atoms/user.atom";
import { useAtomValue } from "jotai";
import { JSX } from "react";
import { AdminScreen } from "../../screens/admin";

export const DynamicDashboard = () => {
	const role = useAtomValue(getUserMainRole);
	if (!role) return null;
	const roleScreens: Record<TRoleList, JSX.Element> = {
		Administrador: <AdminScreen />,
		Técnico: <>Técnico tela</>,
		ADV: <>ADV tela</>,
		Usuário: <>Usuário tela</>,
	};
	return roleScreens[role] || null;
};
