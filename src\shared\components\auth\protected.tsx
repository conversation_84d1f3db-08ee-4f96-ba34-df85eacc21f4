"use client";

import { IAuthorizableEntity, TPermissionAction, TPermissionSubject } from "@/config/permissions/types";
import { usePermissions } from "@/shared/hooks/permissions/permissions.hook";
import React, { ReactNode } from "react";

interface IProtectedComponentProps {
	action: TPermissionAction;
	subject: TPermissionSubject;
	entity?: IAuthorizableEntity;
	children: ReactNode;
	fallback?: ReactNode;
	renderOnError?: boolean;
}

export const ProtectedComponent: React.FC<IProtectedComponentProps> = ({
	action,
	subject,
	entity,
	children,
	fallback = null,
	renderOnError = false,
}) => {
	const { can } = usePermissions();
	try {
		if (can(action, subject, entity)) return <>{children}</>;
		return <>{fallback}</>;
	} catch (error) {
		console.error("Erro ao verificar permissões:", error);
		return renderOnError ? <>{fallback}</> : null;
	}
};

interface IProtectedByAnyPermissionProps {
	permissions: Array<{
		action: TPermissionAction;
		subject: TPermissionSubject;
		entity?: IAuthorizableEntity;
	}>;
	children: ReactNode;
	fallback?: ReactNode;
	renderOnError?: boolean;
}

export const ProtectedByAnyPermission: React.FC<IProtectedByAnyPermissionProps> = ({
	permissions,
	children,
	fallback = null,
	renderOnError = false,
}) => {
	const { hasAnyPermission } = usePermissions();
	try {
		if (hasAnyPermission(permissions)) return <>{children}</>;
		return <>{fallback}</>;
	} catch (error) {
		console.error("Erro ao verificar permissões:", error);
		return renderOnError ? <>{fallback}</> : null;
	}
};

interface IProtectedByAllPermissionsProps {
	permissions: Array<{
		action: TPermissionAction;
		subject: TPermissionSubject;
		entity?: IAuthorizableEntity;
	}>;
	children: ReactNode;
	fallback?: ReactNode;
	renderOnError?: boolean;
}

export const ProtectedByAllPermissions: React.FC<IProtectedByAllPermissionsProps> = ({
	permissions,
	children,
	fallback = null,
	renderOnError = false,
}) => {
	const { hasAllPermissions } = usePermissions();
	try {
		if (hasAllPermissions(permissions)) return <>{children}</>;
		return <>{fallback}</>;
	} catch (error) {
		console.error("Erro ao verificar permissões:", error);
		return renderOnError ? <>{fallback}</> : null;
	}
};

interface IProtectedAdminComponentProps {
	children: ReactNode;
	fallback?: ReactNode;
}

export const ProtectedAdminComponent: React.FC<IProtectedAdminComponentProps> = ({ children, fallback = null }) => {
	return (
		<ProtectedComponent action="manage" subject="all" fallback={fallback}>
			{children}
		</ProtectedComponent>
	);
};
