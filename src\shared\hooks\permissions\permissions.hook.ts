import { IAuthorizableEntity, TPermissionAction, TPermissionSubject } from "@/config/permissions/types";
import { useUser } from "@/core/auth/hooks/user/user.hook";
import { useCallback, useMemo } from "react";

interface IUsePermissionsReturn {
	can: (action: TPermissionAction, subject: TPermissionSubject, entity?: IAuthorizableEntity) => boolean;
	cannot: (action: TPermissionAction, subject: TPermissionSubject, entity?: IAuthorizableEntity) => boolean;
	canCreate: (subject: TPermissionSubject) => boolean;
	canRead: (subject: TPermissionSubject, entity?: IAuthorizableEntity) => boolean;
	canUpdate: (subject: TPermissionSubject, entity?: IAuthorizableEntity) => boolean;
	canDelete: (subject: TPermissionSubject, entity?: IAuthorizableEntity) => boolean;
	canManage: (subject: TPermissionSubject) => boolean;
	hasAnyPermission: (permissions: Array<{ action: TPermissionAction; subject: TPermissionSubject }>) => boolean;
	hasAllPermissions: (permissions: Array<{ action: TPermissionAction; subject: TPermissionSubject }>) => boolean;
}

export const usePermissions = (): IUsePermissionsReturn => {
	const { ability } = useUser();

	const extractEntityId = (entity?: IAuthorizableEntity): string | undefined => {
		return entity?.id ?? entity?.authorId ?? entity?.userId ?? entity?.ownerId;
	};

	const can = useCallback(
		(action: TPermissionAction, subject: TPermissionSubject, entity?: IAuthorizableEntity): boolean => {
			const entityId = extractEntityId(entity);
			return ability.can(action, subject, entityId);
		},
		[ability]
	);

	const cannot = useCallback(
		(action: TPermissionAction, subject: TPermissionSubject, entity?: IAuthorizableEntity): boolean => {
			const entityId = extractEntityId(entity);
			return ability.cannot(action, subject, entityId);
		},
		[ability]
	);

	const canCreate = useCallback(
		(subject: TPermissionSubject): boolean => {
			return can("create", subject);
		},
		[can]
	);

	const canRead = useCallback(
		(subject: TPermissionSubject, entity?: IAuthorizableEntity): boolean => {
			return can("read", subject, entity);
		},
		[can]
	);

	const canUpdate = useCallback(
		(subject: TPermissionSubject, entity?: IAuthorizableEntity): boolean => {
			return can("update", subject, entity);
		},
		[can]
	);

	const canDelete = useCallback(
		(subject: TPermissionSubject, entity?: IAuthorizableEntity): boolean => {
			return can("delete", subject, entity);
		},
		[can]
	);

	const canManage = useCallback(
		(subject: TPermissionSubject): boolean => {
			return can("manage", subject);
		},
		[can]
	);

	const hasAnyPermission = useCallback(
		(permissions: Array<{ action: TPermissionAction; subject: TPermissionSubject }>): boolean => {
			return permissions.some(({ action, subject }) => can(action, subject));
		},
		[can]
	);

	const hasAllPermissions = useCallback(
		(permissions: Array<{ action: TPermissionAction; subject: TPermissionSubject }>): boolean => {
			return permissions.every(({ action, subject }) => can(action, subject));
		},
		[can]
	);

	return useMemo(
		() => ({
			can,
			cannot,
			canCreate,
			canRead,
			canUpdate,
			canDelete,
			canManage,
			hasAnyPermission,
			hasAllPermissions,
		}),
		[can, cannot, canCreate, canRead, canUpdate, canDelete, canManage, hasAnyPermission, hasAllPermissions]
	);
};
