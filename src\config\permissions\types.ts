import { MongoAbility, Subject } from "@casl/ability";

export interface IDefineCaslPermission {
	action: TPermissionAction;
	subject: TPermissionSubject;
}

export const ACTION_LIST = {
	READ: "read",
	CREATE: "create",
	UPDATE: "update",
	DELETE: "delete",
	MANAGE: "manage",
} as const;
export type TPermissionAction = (typeof ACTION_LIST)[keyof typeof ACTION_LIST];

export const SUBJECT_LIST = {
	ORDER: "order",
	USER: "user",
	POST: "post",
	ALL: "all",
	PRODUCTION: "production",
} as const;
export type TPermissionSubject = (typeof SUBJECT_LIST)[keyof typeof SUBJECT_LIST];

export const ROLE_LIST = {
	ADMINISTRATOR: "Administrador",
	TECNICO: "Técnico",
	ADV: "ADV",
	USER: "Usuário",
} as const;
export type TRoleList = (typeof ROLE_LIST)[keyof typeof ROLE_LIST];

export type Actions = TPermissionAction;
export type Subjects = TPermissionSubject;
export type AppAbility = MongoAbility<[Actions, Subjects | Subject]>;

export interface IAuthorizableEntity {
	id?: string;
	authorId?: string;
	userId?: string;
	ownerId?: string;
}
