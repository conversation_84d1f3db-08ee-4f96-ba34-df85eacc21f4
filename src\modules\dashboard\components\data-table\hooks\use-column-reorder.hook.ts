import { useLocalStorage } from "@/shared/hooks/utils";
import { DragEndEvent } from "@dnd-kit/core";
import { arrayMove } from "@dnd-kit/sortable";
import { ColumnDef } from "@tanstack/react-table";
import { useCallback, useMemo } from "react";

// Interface following the naming convention: I<Action><Name><Scope>
export interface IHandleColumnReorderHook<TData> {
	orderedColumns: ColumnDef<TData>[];
	columnIds: string[];
	handleColumnDragEnd: (event: DragEndEvent) => void;
	resetColumnOrder: () => void;
}

interface IColumnReorderOptions {
	storageKey?: string;
	fixedColumnIds?: string[];
}

/**
 * Custom hook for managing column reordering with localStorage persistence
 * Follows SOLID principles:
 * - Single Responsibility: Manages only column reordering logic
 * - Open/Closed: Extensible through options parameter
 * - Dependency Inversion: Depends on abstractions (useLocalStorage)
 */
export const useColumnReorder = <TData>(columns: ColumnDef<TData>[], options: IColumnReorderOptions = {}): IHandleColumnReorderHook<TData> => {
	const { storageKey = "data-table-column-order", fixedColumnIds = ["drag", "select", "actions"] } = options;

	// Get initial column order from localStorage or use default order
	const getInitialColumnOrder = useCallback((): string[] => {
		return columns.map(col => col.id || "unknown");
	}, [columns]);

	const [columnOrder, setColumnOrder] = useLocalStorage<string[]>(storageKey, getInitialColumnOrder());

	// Separate fixed and reorderable columns
	const { fixedColumns, reorderableColumns } = useMemo(() => {
		const fixed = columns.filter(col => fixedColumnIds.includes(col.id || ""));
		const reorderable = columns.filter(col => !fixedColumnIds.includes(col.id || ""));

		return {
			fixedColumns: fixed,
			reorderableColumns: reorderable,
		};
	}, [columns, fixedColumnIds]);

	// Create ordered columns based on saved order
	const orderedColumns = useMemo((): ColumnDef<TData>[] => {
		// Create a map for quick column lookup
		const columnMap = new Map(columns.map(col => [col.id || "", col]));

		// Start with the saved order, but filter out invalid/missing columns
		const validSavedOrder = columnOrder.filter(id => columnMap.has(id));

		// Add any new columns that aren't in the saved order
		const missingColumns = columns.filter(col => !validSavedOrder.includes(col.id || "")).map(col => col.id || "");

		// Combine saved order with missing columns
		const fullOrder = [...validSavedOrder, ...missingColumns];

		// Build the final ordered columns array
		return fullOrder.map(id => columnMap.get(id)).filter((col): col is ColumnDef<TData> => col !== undefined);
	}, [columns, columnOrder]);

	// Get column IDs for drag and drop context
	const columnIds = useMemo((): string[] => {
		return reorderableColumns.map(col => col.id || "");
	}, [reorderableColumns]);

	// Handle column drag end event
	const handleColumnDragEnd = useCallback(
		(event: DragEndEvent): void => {
			const { active, over } = event;

			if (!active || !over || active.id === over.id) return;

			const activeId = active.id as string;
			const overId = over.id as string;

			// Only allow reordering of non-fixed columns
			if (fixedColumnIds.includes(activeId) || fixedColumnIds.includes(overId)) {
				return;
			}

			const reorderableIds = reorderableColumns.map(col => col.id || "");
			const oldIndex = reorderableIds.indexOf(activeId);
			const newIndex = reorderableIds.indexOf(overId);

			if (oldIndex === -1 || newIndex === -1) return;

			const newOrder = arrayMove(reorderableIds, oldIndex, newIndex);

			// Update the full column order including fixed columns
			const updatedOrder = [...fixedColumnIds.filter(id => columns.some(col => col.id === id)), ...newOrder];

			setColumnOrder(updatedOrder);
		},
		[fixedColumnIds, reorderableColumns, setColumnOrder, columns]
	);

	// Reset column order to default
	const resetColumnOrder = useCallback((): void => {
		const defaultOrder = getInitialColumnOrder();
		setColumnOrder(defaultOrder);
	}, [getInitialColumnOrder, setColumnOrder]);

	return {
		orderedColumns,
		columnIds,
		handleColumnDragEnd,
		resetColumnOrder,
	};
};
