import { TableHead } from "@/shared/components/shadcn/table";
import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { Header, flexRender } from "@tanstack/react-table";
import { GripVertical } from "lucide-react";
import { ReactNode } from "react";

interface IDraggableColumnHeaderProps<TData> {
	header: Header<TData, unknown>;
	children?: ReactNode;
	isDraggable?: boolean;
}

/**
 * Draggable column header component that wraps table headers
 * Follows SOLID principles:
 * - Single Responsibility: Handles only column header drag functionality
 * - Open/Closed: Can be extended with additional props
 * - Interface Segregation: Minimal interface focused on drag functionality
 */
export function DraggableColumnHeader<TData>({ header, children, isDraggable = true }: IDraggableColumnHeaderProps<TData>) {
	const columnId = header.column.id;

	// Fixed columns that should not be draggable
	const fixedColumnIds = ["drag", "select", "actions"];
	const isFixedColumn = fixedColumnIds.includes(columnId);
	const shouldBeDraggable = isDraggable && !isFixedColumn;

	const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({
		id: columnId,
		disabled: !shouldBeDraggable,
	});

	const style = {
		transform: CSS.Transform.toString(transform),
		transition,
		opacity: isDragging ? 0.5 : 1,
		cursor: shouldBeDraggable ? "grab" : "default",
	};

	return (
		<TableHead
			key={header.id}
			colSpan={header.colSpan}
			ref={setNodeRef}
			style={style}
			className={`
				relative
				${shouldBeDraggable ? "hover:bg-muted/50" : ""}
				${isDragging ? "z-50 shadow-lg" : ""}
			`}
			{...(shouldBeDraggable ? attributes : {})}
		>
			<div className="flex items-center gap-2">
				{shouldBeDraggable && (
					<button
						type="button"
						className="flex items-center justify-center p-1 rounded hover:bg-muted/50 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
						{...listeners}
						aria-label={`Drag to reorder ${header.column.id} column`}
						tabIndex={0}
					>
						<GripVertical className="h-3 w-3 text-muted-foreground" />
					</button>
				)}
				<div className="flex-1">
					{children || (header.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext()))}
				</div>
			</div>
		</TableHead>
	);
}
