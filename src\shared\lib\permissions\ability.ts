import { ACTION_LIST, AppAbility, SUBJECT_LIST, TPermissionAction, TPermissionSubject } from "@/config/permissions/types";
import { IUser } from "@/core/auth/types/user.types";
import { AbilityBuilder, createMongoAbility } from "@casl/ability";

export const defineAbilitiesFor = (user: IUser | null): AppAbility => {
	const { can, build } = new AbilityBuilder<AppAbility>(createMongoAbility);
	if (!user || !user.permissions) return build();

	const permissions = user.permissions;

	for (const permission of permissions) {
		const { action, subject } = permission;

		if (!isValidAction(action) || !isValidSubject(subject)) {
			console.warn(`Permissão inválida ignorada: ${action}:${subject}`);
			continue;
		}

		if (action === "manage") {
			handleManagePermission(can, subject);
		} else {
			handleSpecificPermission(can, action, subject);
		}
	}

	return build();
};

const handleManagePermission = (can: AbilityBuilder<AppAbility>["can"], subject: TPermissionSubject): void => {
	if (subject === "all") {
		can("manage", "all");
	} else {
		const crudActions: TPermissionAction[] = Object.values(ACTION_LIST);
		crudActions.forEach(crudAction => {
			can(crudAction, subject);
		});
		can("manage", subject);
	}
};

const handleSpecificPermission = (can: AbilityBuilder<AppAbility>["can"], action: TPermissionAction, subject: TPermissionSubject): void => {
	if (subject === "all") {
		can(action, "all");
	} else {
		can(action, subject);
	}
};

const isValidAction = (action: string): action is TPermissionAction => {
	const validActions: TPermissionAction[] = Object.values(ACTION_LIST);
	return validActions.includes(action as TPermissionAction);
};

const isValidSubject = (subject: string): subject is TPermissionSubject => {
	const validSubjects: TPermissionSubject[] = Object.values(SUBJECT_LIST);
	return validSubjects.includes(subject as TPermissionSubject);
};
